﻿// <auto-generated />
using System;
using ElearningBackend;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace ElearningBackend.Migrations
{
    [DbContext(typeof(ElearningDbContext))]
    [Migration("20250812235312_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("ClientCours", b =>
                {
                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<int>("CoursConsultesId")
                        .HasColumnType("int");

                    b.HasKey("ClientId", "CoursConsultesId");

                    b.HasIndex("CoursConsultesId");

                    b.ToTable("ClientCours", (string)null);
                });

            modelBuilder.Entity("ElearningBackend.Models.Certificat", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AdminId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateObtention")
                        .HasColumnType("datetime2");

                    b.Property<string>("NomClient")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AdminId");

                    b.ToTable("Certificats");
                });

            modelBuilder.Entity("ElearningBackend.Models.Contenu", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CoursId")
                        .HasColumnType("int");

                    b.Property<bool>("EstPayant")
                        .HasColumnType("bit");

                    b.Property<string>("Fichier")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TypeContenu")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("nvarchar(8)");

                    b.HasKey("Id");

                    b.HasIndex("CoursId");

                    b.ToTable("Contenus");

                    b.HasDiscriminator<string>("TypeContenu").HasValue("Contenu");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("ElearningBackend.Models.Cours", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("EstPayant")
                        .HasColumnType("bit");

                    b.Property<int>("FormateurId")
                        .HasColumnType("int");

                    b.Property<float>("Prix")
                        .HasColumnType("real");

                    b.Property<string>("Titre")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("FormateurId");

                    b.HasIndex("Titre");

                    b.ToTable("Cours");
                });

            modelBuilder.Entity("ElearningBackend.Models.ResultatQuiz", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateSoumission")
                        .HasColumnType("datetime2");

                    b.Property<int>("QuizId")
                        .HasColumnType("int");

                    b.Property<int>("Score")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.HasIndex("QuizId");

                    b.ToTable("ResultatsQuiz");
                });

            modelBuilder.Entity("ElearningBackend.Models.Utilisateur", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CompteBancaire")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Nom")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Password")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Prenom")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasFilter("[Email] IS NOT NULL");

                    b.ToTable("Utilisateurs");

                    b.HasDiscriminator().HasValue("Utilisateur");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Message", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Contenu")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateEnvoi")
                        .HasColumnType("datetime2");

                    b.Property<int>("DestinataireId")
                        .HasColumnType("int");

                    b.Property<int>("ExpediteurId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DestinataireId");

                    b.HasIndex("ExpediteurId", "DestinataireId");

                    b.ToTable("Messages");
                });

            modelBuilder.Entity("Paiement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<int>("CoursId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DatePaiement")
                        .HasColumnType("datetime2");

                    b.Property<float>("Montant")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.HasIndex("CoursId");

                    b.ToTable("Paiements");
                });

            modelBuilder.Entity("ElearningBackend.Models.Quiz", b =>
                {
                    b.HasBaseType("ElearningBackend.Models.Contenu");

                    b.Property<string>("Niveau")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SeuilMinimalEasy")
                        .HasColumnType("int");

                    b.Property<int>("SeuilMinimalHard")
                        .HasColumnType("int");

                    b.Property<int>("SeuilMinimalMedium")
                        .HasColumnType("int");

                    b.HasDiscriminator().HasValue("Quiz");
                });

            modelBuilder.Entity("ElearningBackend.Models.Resume", b =>
                {
                    b.HasBaseType("ElearningBackend.Models.Contenu");

                    b.HasDiscriminator().HasValue("Resume");
                });

            modelBuilder.Entity("ElearningBackend.Models.Video", b =>
                {
                    b.HasBaseType("ElearningBackend.Models.Contenu");

                    b.HasDiscriminator().HasValue("Video");
                });

            modelBuilder.Entity("ElearningBackend.Models.Admin", b =>
                {
                    b.HasBaseType("ElearningBackend.Models.Utilisateur");

                    b.Property<bool>("EstVerifie")
                        .HasColumnType("bit");

                    b.HasDiscriminator().HasValue("Admin");
                });

            modelBuilder.Entity("ElearningBackend.Models.Client", b =>
                {
                    b.HasBaseType("ElearningBackend.Models.Utilisateur");

                    b.HasDiscriminator().HasValue("Client");
                });

            modelBuilder.Entity("ElearningBackend.Models.Formateur", b =>
                {
                    b.HasBaseType("ElearningBackend.Models.Client");

                    b.Property<string>("Cv")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Diplome")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("EstValide")
                        .HasColumnType("bit");

                    b.HasDiscriminator().HasValue("Formateur");
                });

            modelBuilder.Entity("ClientCours", b =>
                {
                    b.HasOne("ElearningBackend.Models.Client", null)
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElearningBackend.Models.Cours", null)
                        .WithMany()
                        .HasForeignKey("CoursConsultesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ElearningBackend.Models.Certificat", b =>
                {
                    b.HasOne("ElearningBackend.Models.Admin", "Admin")
                        .WithMany("CertificatsGeneres")
                        .HasForeignKey("AdminId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Admin");
                });

            modelBuilder.Entity("ElearningBackend.Models.Contenu", b =>
                {
                    b.HasOne("ElearningBackend.Models.Cours", "Cours")
                        .WithMany("Contenus")
                        .HasForeignKey("CoursId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Cours");
                });

            modelBuilder.Entity("ElearningBackend.Models.Cours", b =>
                {
                    b.HasOne("ElearningBackend.Models.Formateur", "Formateur")
                        .WithMany("CoursCree")
                        .HasForeignKey("FormateurId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Formateur");
                });

            modelBuilder.Entity("ElearningBackend.Models.ResultatQuiz", b =>
                {
                    b.HasOne("ElearningBackend.Models.Client", "Client")
                        .WithMany("ResultatsQuiz")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElearningBackend.Models.Quiz", "Quiz")
                        .WithMany()
                        .HasForeignKey("QuizId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");

                    b.Navigation("Quiz");
                });

            modelBuilder.Entity("Message", b =>
                {
                    b.HasOne("ElearningBackend.Models.Utilisateur", "Destinataire")
                        .WithMany()
                        .HasForeignKey("DestinataireId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElearningBackend.Models.Utilisateur", "Expediteur")
                        .WithMany()
                        .HasForeignKey("ExpediteurId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Destinataire");

                    b.Navigation("Expediteur");
                });

            modelBuilder.Entity("Paiement", b =>
                {
                    b.HasOne("ElearningBackend.Models.Client", "Client")
                        .WithMany("Paiements")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElearningBackend.Models.Cours", "Cours")
                        .WithMany()
                        .HasForeignKey("CoursId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");

                    b.Navigation("Cours");
                });

            modelBuilder.Entity("ElearningBackend.Models.Cours", b =>
                {
                    b.Navigation("Contenus");
                });

            modelBuilder.Entity("ElearningBackend.Models.Admin", b =>
                {
                    b.Navigation("CertificatsGeneres");
                });

            modelBuilder.Entity("ElearningBackend.Models.Client", b =>
                {
                    b.Navigation("Paiements");

                    b.Navigation("ResultatsQuiz");
                });

            modelBuilder.Entity("ElearningBackend.Models.Formateur", b =>
                {
                    b.Navigation("CoursCree");
                });
#pragma warning restore 612, 618
        }
    }
}
