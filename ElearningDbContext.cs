﻿using ElearningBackend.Models;
using Microsoft.EntityFrameworkCore;

namespace ElearningBackend
{
    public class ElearningDbContext : DbContext
    {
        public ElearningDbContext(DbContextOptions<ElearningDbContext> options) : base(options) { }

        // ✅ Tables principales
        public DbSet<Utilisateur> Utilisateurs { get; set; }
        public DbSet<Client> Clients { get; set; }
        public DbSet<Formateur> Formateurs { get; set; }
        public DbSet<Admin> Admins { get; set; }

        public DbSet<Cours> Cours { get; set; }
        public DbSet<Contenu> Contenus { get; set; }
        public DbSet<Quiz> Quizs { get; set; }
        public DbSet<Video> Videos { get; set; }
        public DbSet<Resume> Resumes { get; set; }

        public DbSet<Paiement> Paiements { get; set; }
        public DbSet<Certificat> Certificats { get; set; }
        public DbSet<ResultatQuiz> ResultatsQuiz { get; set; }
        public DbSet<Message> Messages { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // ✅ Configuration de l'héritage TPH pour Utilisateur
            modelBuilder.Entity<Utilisateur>()
                .HasDiscriminator<string>("Discriminator")
                .HasValue<Client>("Client")
                .HasValue<Formateur>("Formateur")
                .HasValue<Admin>("Admin");

            // ✅ Configuration de l'héritage TPH pour Contenu
            modelBuilder.Entity<Contenu>()
                .HasDiscriminator<string>("TypeContenu")
                .HasValue<Quiz>("Quiz")
                .HasValue<Video>("Video")
                .HasValue<Resume>("Resume");

            // ✅ Configuration des relations Certificat
            modelBuilder.Entity<Certificat>()
                .HasOne(c => c.Admin)
                .WithMany(a => a.CertificatsGeneres)
                .HasForeignKey(c => c.AdminId)
                .OnDelete(DeleteBehavior.Restrict);

            // ✅ Configuration des relations Message
            modelBuilder.Entity<Message>()
                .HasOne(m => m.Expediteur)
                .WithMany()
                .HasForeignKey(m => m.ExpediteurId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Message>()
                .HasOne(m => m.Destinataire)
                .WithMany()
                .HasForeignKey(m => m.DestinataireId)
                .OnDelete(DeleteBehavior.Restrict);

            // ✅ Configuration des relations Paiement
            modelBuilder.Entity<Paiement>()
                .HasOne(p => p.Client)
                .WithMany(c => c.Paiements)
                .HasForeignKey(p => p.ClientId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Paiement>()
                .HasOne(p => p.Cours)
                .WithMany()
                .HasForeignKey(p => p.CoursId)
                .OnDelete(DeleteBehavior.Cascade);

            // ✅ Configuration des relations ResultatQuiz
            modelBuilder.Entity<ResultatQuiz>()
                .HasOne(rq => rq.Client)
                .WithMany(c => c.ResultatsQuiz)
                .HasForeignKey(rq => rq.ClientId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ResultatQuiz>()
                .HasOne(rq => rq.Quiz)
                .WithMany()
                .HasForeignKey(rq => rq.QuizId)
                .OnDelete(DeleteBehavior.Cascade);

            // ✅ Configuration des relations Cours
            modelBuilder.Entity<Cours>()
                .HasOne(c => c.Formateur)
                .WithMany(f => f.CoursCree)
                .HasForeignKey(c => c.FormateurId)
                .OnDelete(DeleteBehavior.Restrict);

            // ✅ Configuration des relations Contenu
            modelBuilder.Entity<Contenu>()
                .HasOne(c => c.Cours)
                .WithMany(co => co.Contenus)
                .HasForeignKey(c => c.CoursId)
                .OnDelete(DeleteBehavior.Cascade);

            // ✅ Configuration Many-to-Many Client-Cours (CoursConsultes)
            modelBuilder.Entity<Client>()
                .HasMany(c => c.CoursConsultes)
                .WithMany()
                .UsingEntity(j => j.ToTable("ClientCours"));

            // ✅ Index pour améliorer les performances
            modelBuilder.Entity<Utilisateur>()
                .HasIndex(u => u.Email)
                .IsUnique();

            modelBuilder.Entity<Cours>()
                .HasIndex(c => c.Titre);

            modelBuilder.Entity<Message>()
                .HasIndex(m => new { m.ExpediteurId, m.DestinataireId });
        }
    }
}
