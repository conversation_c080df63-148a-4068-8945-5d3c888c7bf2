﻿using System.ComponentModel.DataAnnotations;

namespace ElearningBackend.Models
{
    public class ContenuCreationRequest
    {
        [Required]
        public string TypeContenu { get; set; } = string.Empty; // "Video", "Resume", "Quiz"

        [Required]
        public string Fichier { get; set; } = string.Empty;

        public bool EstPayant { get; set; }

        // Propriétés spécifiques aux vidéos
        public int? Duree { get; set; }

        // Propriétés spécifiques aux quiz
        public string? Niveau { get; set; }
        public int? SeuilMinimalEasy { get; set; }
        public int? SeuilMinimalMedium { get; set; }
        public int? SeuilMinimalHard { get; set; }
    }
}
