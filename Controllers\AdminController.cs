﻿using ElearningBackend.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace ElearningBackend.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")] // ✅ Seuls les admins peuvent accéder
    public class AdminController : ControllerBase
    {
        private readonly ElearningDbContext _context;

        public AdminController(ElearningDbContext context)
        {
            _context = context;
        }

        // GET: api/Admin - Liste tous les admins
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Admin>>> GetAllAdmins()
        {
            var admins = await _context.Utilisateurs
                .OfType<Admin>()
                .Include(a => a.CertificatsGeneres)
                .Select(a => new Admin
                {
                    Id = a.Id,
                    Email = a.Email,
                    Nom = a.Nom,
                    Prenom = a.Prenom,
                    CompteBancaire = a.CompteBancaire,
                    EstVerifie = a.EstVerifie,
                    CertificatsGeneres = a.CertificatsGeneres
                    // ✅ Password exclu pour la sécurité
                })
                .ToListAsync();

            return Ok(admins);
        }

        // GET: api/Admin/5 - Un admin par ID
        [HttpGet("{id}")]
        public async Task<ActionResult<Admin>> GetAdmin(int id)
        {
            var admin = await _context.Utilisateurs
                .OfType<Admin>()
                .Include(a => a.CertificatsGeneres)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (admin == null)
                return NotFound(new { Message = "Admin introuvable." });

            // ✅ Retourner sans le mot de passe
            var adminResponse = new Admin
            {
                Id = admin.Id,
                Email = admin.Email,
                Nom = admin.Nom,
                Prenom = admin.Prenom,
                CompteBancaire = admin.CompteBancaire,
                EstVerifie = admin.EstVerifie,
                CertificatsGeneres = admin.CertificatsGeneres
            };

            return Ok(adminResponse);
        }

        // ✅ POST: api/Admin - Créer un nouvel admin (seul un admin peut créer un autre admin)
        [HttpPost]
        public async Task<ActionResult<Admin>> CreateAdmin(AdminRegistrationRequest request)
        {
            // ✅ Vérifier si l'email existe déjà
            if (await _context.Utilisateurs.AnyAsync(u => u.Email == request.Email))
            {
                return BadRequest("Un utilisateur avec cet email existe déjà.");
            }

            // ✅ Créer le nouvel admin
            var admin = new Admin
            {
                Email = request.Email,
                Nom = request.Nom,
                Prenom = request.Prenom,
                Password = AuthController.HashPassword(request.Password),
                CompteBancaire = request.CompteBancaire ?? "",
                EstVerifie = false, // Le nouvel admin doit être vérifié
                CertificatsGeneres = new List<Certificat>()
            };

            _context.Utilisateurs.Add(admin);
            await _context.SaveChangesAsync();

            // ✅ Retourner l'admin sans le mot de passe
            var adminResponse = new Admin
            {
                Id = admin.Id,
                Email = admin.Email,
                Nom = admin.Nom,
                Prenom = admin.Prenom,
                CompteBancaire = admin.CompteBancaire,
                EstVerifie = admin.EstVerifie,
                CertificatsGeneres = admin.CertificatsGeneres
            };

            return CreatedAtAction(nameof(GetAdmin), new { id = admin.Id }, adminResponse);
        }

        // PUT: api/Admin/5/verify - Vérifier un admin
        [HttpPut("{id}/verify")]
        public async Task<IActionResult> VerifyAdmin(int id)
        {
            var admin = await _context.Utilisateurs
                .OfType<Admin>()
                .FirstOrDefaultAsync(a => a.Id == id);

            if (admin == null)
                return NotFound(new { Message = "Admin introuvable." });

            admin.EstVerifie = true;
            await _context.SaveChangesAsync();

            return Ok(new { Message = "Admin vérifié avec succès.", AdminId = admin.Id });
        }

        // DELETE: api/Admin/5 - Supprimer un admin
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteAdmin(int id)
        {
            var admin = await _context.Utilisateurs
                .OfType<Admin>()
                .FirstOrDefaultAsync(a => a.Id == id);

            if (admin == null)
                return NotFound();

            // ✅ Empêcher la suppression de l'admin par défaut
            if (admin.Email == "<EMAIL>")
            {
                return BadRequest("Impossible de supprimer l'admin par défaut.");
            }

            _context.Utilisateurs.Remove(admin);
            await _context.SaveChangesAsync();

            return Ok(new { Message = "Admin supprimé avec succès." });
        }

        // POST: api/Admin/generer-certificat
        [HttpPost("generer-certificat")]
        public async Task<ActionResult<Certificat>> GenererCertificat([FromBody] GenerateCertificateRequest request)
        {
            // Récupérer l'ID de l'admin connecté depuis le token JWT
            var adminIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(adminIdClaim, out int adminId))
            {
                return Unauthorized("Admin non identifié.");
            }

            var certificat = new Certificat
            {
                NomClient = $"{request.PrenomClient} {request.NomClient}",
                DateObtention = DateTime.Now,
                AdminId = adminId
            };

            _context.Certificats.Add(certificat);
            await _context.SaveChangesAsync();

            return Ok(certificat);
        }

        // POST: api/Admin/valider-formateur
        [HttpPost("valider-formateur")]
        public async Task<ActionResult> ValiderFormateur([FromBody] int formateurId)
        {
            var formateur = await _context.Utilisateurs
                .OfType<Formateur>()
                .FirstOrDefaultAsync(f => f.Id == formateurId);

            if (formateur == null)
                return NotFound("Formateur introuvable.");

            formateur.EstValide = true;
            await _context.SaveChangesAsync();

            return Ok(new { Message = "Formateur validé avec succès.", FormateurId = formateur.Id });
        }

        // GET: api/Admin/statistiques
        [HttpGet("statistiques")]
        public async Task<ActionResult<object>> VoirStatistiques()
        {
            var totalUtilisateurs = await _context.Utilisateurs.CountAsync();
            var totalFormateurs = await _context.Utilisateurs.OfType<Formateur>().CountAsync();
            var totalClients = await _context.Utilisateurs.OfType<Client>().CountAsync();
            var totalAdmins = await _context.Utilisateurs.OfType<Admin>().CountAsync();
            var totalCours = await _context.Cours.CountAsync();
            var totalPaiements = await _context.Paiements.CountAsync();
            var revenuTotal = await _context.Paiements.SumAsync(p => (decimal)p.Montant);

            var stats = new
            {
                TotalUtilisateurs = totalUtilisateurs,
                TotalFormateurs = totalFormateurs,
                TotalClients = totalClients,
                TotalAdmins = totalAdmins,
                TotalCours = totalCours,
                TotalPaiements = totalPaiements,
                RevenuTotal = revenuTotal,
                DateGeneration = DateTime.Now
            };

            return Ok(stats);
        }

        // GET: api/Admin/formateurs-en-attente
        [HttpGet("formateurs-en-attente")]
        public async Task<ActionResult<IEnumerable<Formateur>>> GetFormateursEnAttente()
        {
            var formateurs = await _context.Utilisateurs
                .OfType<Formateur>()
                .Where(f => !f.EstValide)
                .Include(f => f.CoursCree)
                .Select(f => new Formateur
                {
                    Id = f.Id,
                    Email = f.Email,
                    Nom = f.Nom,
                    Prenom = f.Prenom,
                    Cv = f.Cv,
                    Diplome = f.Diplome,
                    EstValide = f.EstValide,
                    CoursCree = f.CoursCree
                    // ✅ Password exclu
                })
                .ToListAsync();

            return Ok(formateurs);
        }
    }

    // ✅ Modèle pour l'inscription d'admin
    public class AdminRegistrationRequest
    {
        public string Email { get; set; } = string.Empty;
        public string Nom { get; set; } = string.Empty;
        public string Prenom { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string? CompteBancaire { get; set; }
    }

    // ✅ Modèle pour la génération de certificat (nom changé pour éviter le conflit)
    public class GenerateCertificateRequest
    {
        public string NomClient { get; set; } = string.Empty;
        public string PrenomClient { get; set; } = string.Empty;
        public string TitreCours { get; set; } = string.Empty;
        public int Score { get; set; }
    }
}