﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ElearningBackend.Models;

namespace ElearningBackend.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class FormateurController : ControllerBase
    {
        private readonly ElearningDbContext _context;

        public FormateurController(ElearningDbContext context)
        {
            _context = context;
        }

        // GET: Liste des formateurs
        [HttpGet]
        public async Task<IActionResult> GetAllFormateurs()
        {
            var formateurs = await _context.Utilisateurs
                .OfType<Formateur>()
                .Include(f => f.CoursCree)
                .ToListAsync();
            return Ok(formateurs);
        }

        // GET: Un formateur par ID
        [HttpGet("{id}")]
        public async Task<IActionResult> GetFormateur(int id)
        {
            var formateur = await _context.Utilisateurs
                .OfType<Formateur>()
                .Include(f => f.Cours<PERSON>ree)
                .FirstOrDefaultAsync(f => f.Id == id);

            if (formateur == null)
                return NotFound(new { Message = "Formateur introuvable." });

            return Ok(formateur);
        }

        // ✅ POST: Créer un formateur avec mot de passe
        [HttpPost]
        public async Task<ActionResult<Formateur>> PostFormateur(FormateurRegistrationRequest request)
        {
            // ✅ Vérifier si l'email existe déjà
            if (await _context.Utilisateurs.AnyAsync(u => u.Email == request.Email))
            {
                return BadRequest("Un utilisateur avec cet email existe déjà.");
            }

            // ✅ Créer le formateur avec mot de passe haché
            var formateur = new Formateur
            {
                Email = request.Email,
                Nom = request.Nom,
                Prenom = request.Prenom,
                Password = AuthController.HashPassword(request.Password), // ✅ Hacher le mot de passe
                CompteBancaire = request.CompteBancaire ?? "",
                Cv = request.Cv ?? "",
                Diplome = request.Diplome ?? "",
                EstValide = false,
                CoursCree = new List<Cours>(),
                Paiements = new List<Paiement>(),
                CoursConsultes = new List<Cours>(),
                ResultatsQuiz = new List<ResultatQuiz>()
            };

            _context.Utilisateurs.Add(formateur);
            await _context.SaveChangesAsync();

            // ✅ Retourner le formateur sans le mot de passe
            var formateurResponse = new Formateur
            {
                Id = formateur.Id,
                Email = formateur.Email,
                Nom = formateur.Nom,
                Prenom = formateur.Prenom,
                CompteBancaire = formateur.CompteBancaire,
                Cv = formateur.Cv,
                Diplome = formateur.Diplome,
                EstValide = formateur.EstValide,
                CoursCree = formateur.CoursCree,
                Paiements = formateur.Paiements,
                CoursConsultes = formateur.CoursConsultes,
                ResultatsQuiz = formateur.ResultatsQuiz
            };

            return CreatedAtAction(nameof(GetFormateur), new { id = formateur.Id }, formateurResponse);
        }

        // POST: Déposer CV et diplôme
        [HttpPost("{id}/deposer-documents")]
        public async Task<IActionResult> DeposerDocuments(int id, [FromBody] Formateur input)
        {
            var formateur = await _context.Utilisateurs
                .OfType<Formateur>()
                .FirstOrDefaultAsync(f => f.Id == id);

            if (formateur == null)
                return NotFound(new { Message = "Formateur introuvable." });

            formateur.Cv = input.Cv;
            formateur.Diplome = input.Diplome;

            await _context.SaveChangesAsync();
            return Ok(new { Message = "Documents déposés avec succès.", formateur });
        }

        // POST: Ajouter un cours
        [HttpPost("{id}/ajouter-cours")]
        public async Task<IActionResult> AjouterCours(int id, [FromBody] CoursCreationRequest request)
        {
            var formateur = await _context.Utilisateurs
                .OfType<Formateur>()
                .FirstOrDefaultAsync(f => f.Id == id);

            if (formateur == null)
                return NotFound(new { Message = "Formateur introuvable." });

            // ✅ Créer le cours à partir de la requête
            var cours = new Cours
            {
                Titre = request.Titre,
                Description = request.Description,
                Duree = request.Duree,
                Niveau = request.Niveau,
                EstPayant = request.EstPayant,
                Prix = request.Prix,
                FormateurId = id,
                Contenus = new List<Contenu>()
            };

            // ✅ Créer les contenus selon leur type
            foreach (var contenuRequest in request.Contenus)
            {
                Contenu contenu = contenuRequest.TypeContenu switch
                {
                    "Video" => new Video
                    {
                        Fichier = contenuRequest.Fichier,
                        EstPayant = contenuRequest.EstPayant,
                        Duree = contenuRequest.Duree ?? 0
                    },
                    "Resume" => new Resume
                    {
                        Fichier = contenuRequest.Fichier,
                        EstPayant = contenuRequest.EstPayant
                    },
                    "Quiz" => new Quiz
                    {
                        Fichier = contenuRequest.Fichier,
                        EstPayant = contenuRequest.EstPayant,
                        Niveau = contenuRequest.Niveau ?? "Easy",
                        SeuilMinimalEasy = contenuRequest.SeuilMinimalEasy ?? 50,
                        SeuilMinimalMedium = contenuRequest.SeuilMinimalMedium ?? 70,
                        SeuilMinimalHard = contenuRequest.SeuilMinimalHard ?? 85
                    },
                    _ => throw new ArgumentException($"Type de contenu non supporté: {contenuRequest.TypeContenu}")
                };

                cours.Contenus.Add(contenu);
            }

            _context.Cours.Add(cours);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetCours", "Cours", new { id = cours.Id }, cours);
        }

        // DELETE: Supprimer un formateur
        [HttpDelete("{id}")]
        public async Task<IActionResult> SupprimerFormateur(int id)
        {
            var formateur = await _context.Utilisateurs
                .OfType<Formateur>()
                .FirstOrDefaultAsync(f => f.Id == id);

            if (formateur == null)
                return NotFound();

            _context.Utilisateurs.Remove(formateur);
            await _context.SaveChangesAsync();

            return Ok(new { Message = "Formateur supprimé avec succès." });
        }
    }
}
