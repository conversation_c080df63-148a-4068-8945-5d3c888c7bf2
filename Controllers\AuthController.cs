﻿using ElearningBackend.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace ElearningBackend.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly ElearningDbContext _context;
        private readonly IConfiguration _config;

        public AuthController(ElearningDbContext context, IConfiguration config)
        {
            _context = context;
            _config = config;
        }

        [HttpPost("login")]
        public IActionResult Login([FromBody] LoginRequest request)
        {
            // ✅ Chercher l'utilisateur par email
            var user = _context.Utilisateurs.FirstOrDefault(u => u.Email == request.Email);
            if (user == null)
                return Unauthorized("Utilisateur non trouvé.");

            // ✅ Vérifier le mot de passe
            if (!VerifyPassword(request.Password, user.Password))
                return Unauthorized("Mot de passe incorrect.");

            var role = user switch
            {
                Admin => "Admin",
                Formateur => "Formateur",
                Client => "Client",
                _ => "Utilisateur"
            };

            var token = GenerateJwtToken(user, role);
            return Ok(new AuthResponse { Token = token, Role = role });
        }

        // ✅ Méthode pour hacher le mot de passe
        public static string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        // ✅ Méthode pour vérifier le mot de passe
        private bool VerifyPassword(string password, string hashedPassword)
        {
            if (hashedPassword == password)
                return true;

            var hashedInput = HashPassword(password);
            return hashedInput == hashedPassword;
        }

        private string GenerateJwtToken(Utilisateur user, string role)
        {
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_config["Jwt:Key"]));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Email),
                new Claim(ClaimTypes.Email, user.Email),
                new Claim("nom", user.Nom ?? ""),
                new Claim("prenom", user.Prenom ?? ""),
                new Claim(ClaimTypes.Role, role)
            };

            var token = new JwtSecurityToken(
                _config["Jwt:Issuer"],
                _config["Jwt:Issuer"],
                claims,
                expires: DateTime.Now.AddHours(2),
                signingCredentials: creds
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
    }
}