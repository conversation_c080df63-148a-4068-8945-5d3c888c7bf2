﻿using System.ComponentModel.DataAnnotations;

namespace ElearningBackend.Models
{
    public class Cours
    {
        public int Id { get; set; }

        [Required]
        public string Titre { get; set; } = string.Empty;

        public string Description { get; set; } = string.Empty;

        public int Duree { get; set; } // Durée en minutes

        public string Niveau { get; set; } = string.Empty; // "Débutant", "Intermédiaire", "Avancé"

        public bool EstPayant { get; set; }

        public float Prix { get; set; }

        // Relations
        public int FormateurId { get; set; }
        public Formateur Formateur { get; set; } = null!;

        public ICollection<Contenu> Contenus { get; set; } = new List<Contenu>();
    }
}
