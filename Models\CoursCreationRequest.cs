﻿using ElearningBackend.Models;
using System.ComponentModel.DataAnnotations;

public class CoursCreationRequest
{
    [Required]
    public string Titre { get; set; } = string.Empty;

    public string Description { get; set; } = string.Empty;

    [Required]
    public int Duree { get; set; }

    [Required]
    public string Niveau { get; set; } = string.Empty;

    public bool EstPayant { get; set; }

    public float Prix { get; set; }

    // ✅ Liste de contenus simplifiée
    public List<ContenuCreationRequest> Contenus { get; set; } = new List<ContenuCreationRequest>();
}