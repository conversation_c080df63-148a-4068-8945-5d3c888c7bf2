﻿using ElearningBackend.Models;
using ElearningBackend.Controllers;
using Microsoft.EntityFrameworkCore;

namespace ElearningBackend.Services
{
    public class AdminInitializationService
    {
        private readonly ElearningDbContext _context;
        private readonly ILogger<AdminInitializationService> _logger;

        public AdminInitializationService(ElearningDbContext context, ILogger<AdminInitializationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task InitializeDefaultAdminAsync()
        {
            try
            {
                // Vérifier si un admin existe déjà
                var existingAdmin = await _context.Utilisateurs
                    .OfType<Admin>()
                    .FirstOrDefaultAsync(a => a.Email == "<EMAIL>");

                if (existingAdmin == null)
                {
                    // Créer l'admin par défaut
                    var defaultAdmin = new Admin
                    {
                        Email = "<EMAIL>",
                        Nom = "Bouha",
                        Prenom = "Hediya",
                        Password = AuthController.HashPassword("12345azerty"), // Hash du mot de passe
                        CompteBancaire = "",
                        EstVerifie = true, // Admin par défaut vérifié
                        CertificatsGeneres = new List<Certificat>()
                    };

                    _context.Utilisateurs.Add(defaultAdmin);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("✅ Admin par défaut créé avec succès !");
                    _logger.LogInformation("Email: <EMAIL>");
                    _logger.LogInformation("Mot de passe: 12345azerty");
                }
                else
                {
                    _logger.LogInformation("ℹ️ Admin par défaut existe déjà");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erreur lors de la création de l'admin par défaut");
                throw;
            }
        }
    }
}