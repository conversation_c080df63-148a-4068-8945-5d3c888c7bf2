﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ElearningBackend.Migrations
{
    /// <inheritdoc />
    public partial class AddCoursProperties : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "Cours",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "Duree",
                table: "Cours",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "<PERSON>veau",
                table: "Cours",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "Duree",
                table: "Contenus",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Description",
                table: "Cours");

            migrationBuilder.DropColumn(
                name: "<PERSON>ree",
                table: "Cours");

            migrationBuilder.DropColumn(
                name: "Niveau",
                table: "Cours");

            migrationBuilder.DropColumn(
                name: "Duree",
                table: "Contenus");
        }
    }
}
